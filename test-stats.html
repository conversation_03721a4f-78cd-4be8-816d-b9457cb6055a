<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计卡片测试</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dark-theme">
    <div style="padding: 40px; background: var(--background-color); min-height: 100vh;">
        <h1 style="color: var(--text-color); margin-bottom: 30px; text-align: center;">统计卡片样式测试</h1>
        
        <!-- 方案一：经典左对齐布局（推荐） -->
        <div class="creator-detail-stats-section" style="max-width: 800px; margin: 0 auto;">
            <h2 style="color: var(--text-color); margin-bottom: 20px; text-align: center;">方案一：经典左对齐布局（推荐）</h2>
            <div class="creator-detail-stats-grid">
                <div class="creator-detail-stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">28.67万</div>
                        <div class="stat-label">粉丝数量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1.03亿</div>
                        <div class="stat-label">近期观看量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">231.59万</div>
                        <div class="stat-label">近期互动量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">今天</div>
                        <div class="stat-label">最近发布</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案二：现代卡片布局 -->
        <div class="creator-detail-stats-section" style="max-width: 800px; margin: 40px auto 0;">
            <h2 style="color: var(--text-color); margin-bottom: 20px; text-align: center;">方案二：现代卡片布局</h2>
            <div class="creator-detail-stats-grid">
                <div class="creator-detail-stat-item modern-layout">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">162万</div>
                        <div class="stat-label">粉丝数量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item modern-layout">
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1.5亿</div>
                        <div class="stat-label">近期观看量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item modern-layout">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">892万</div>
                        <div class="stat-label">近期互动量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item modern-layout">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">昨天</div>
                        <div class="stat-label">最近发布</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案三：紧凑布局 -->
        <div class="creator-detail-stats-section" style="max-width: 800px; margin: 40px auto 0;">
            <h2 style="color: var(--text-color); margin-bottom: 20px; text-align: center;">方案三：紧凑布局</h2>
            <div class="creator-detail-stats-grid">
                <div class="creator-detail-stat-item compact-layout">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">162万</div>
                        <div class="stat-label">粉丝数量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item compact-layout">
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1.5亿</div>
                        <div class="stat-label">近期观看量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item compact-layout">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">892万</div>
                        <div class="stat-label">近期互动量</div>
                    </div>
                </div>
                <div class="creator-detail-stat-item compact-layout">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">昨天</div>
                        <div class="stat-label">最近发布</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
