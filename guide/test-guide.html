<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引导系统测试 - 优化效果展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="guide-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #f6f8fa 0%, #e3f2fd 50%, #f3e5f5 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #7c3aed, #6366f1);
            color: white;
            padding: 32px;
            text-align: center;
        }
        
        .test-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .test-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 32px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .demo-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .demo-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #1e293b;
        }
        
        .demo-card p {
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #7c3aed, #6366f1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .demo-btn:hover {
            background: linear-gradient(135deg, #7c3aed, #5b21b6);
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(124, 58, 237, 0.4);
        }
        
        .control-panel {
            background: #f1f5f9;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .control-panel h3 {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 1.25rem;
        }
        
        .control-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            background: #ffffff;
            color: #475569;
            border: 1px solid #cbd5e1;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .control-btn:hover {
            background: #f8fafc;
            border-color: #94a3b8;
        }
        
        .control-btn.active {
            background: #7c3aed;
            color: white;
            border-color: #7c3aed;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #7c3aed, #6366f1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .feature-desc {
            color: #64748b;
            font-size: 14px;
        }
        
        /* 模拟页面元素 */
        .sidebar {
            width: 250px;
            height: 300px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .menu-item {
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 6px;
            background: white;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .menu-item:hover {
            background: #f1f5f9;
        }
        
        #ai-assistant-menu {
            background: linear-gradient(135deg, #7c3aed, #6366f1);
            color: white;
            border-color: transparent;
        }
        
        .new-chat {
            background: linear-gradient(135deg, #7c3aed, #6366f1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🎯 引导系统优化展示</h1>
            <p class="test-subtitle">基于 guide card.html 的现代毛玻璃设计</p>
        </div>
        
        <div class="test-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <h3>🎮 引导步骤控制</h3>
                <div class="control-buttons">
                    <button class="control-btn" onclick="showStep(0)">欢迎步骤</button>
                    <button class="control-btn" onclick="showStep(1)">侧边栏介绍</button>
                    <button class="control-btn" onclick="showStep(2)">仪表盘</button>
                    <button class="control-btn" onclick="showStep(3)">产品库</button>
                    <button class="control-btn" onclick="showStep(4)">建联记录</button>
                    <button class="control-btn" onclick="showStep(5)">AI助手</button>
                    <button class="control-btn" onclick="showStep(6)">新建分析</button>
                    <button class="control-btn" onclick="showStep(7)">完成</button>
                    <button class="control-btn" onclick="stopGuide()" style="background: #ef4444; color: white; border-color: #ef4444;">停止引导</button>
                </div>
            </div>
            
            <!-- 特性展示 -->
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-sparkles"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">毛玻璃效果</div>
                        <div class="feature-desc">现代化的backdrop-filter模糊效果</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">渐变设计</div>
                        <div class="feature-desc">优雅的紫色主题渐变色彩</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">响应式布局</div>
                        <div class="feature-desc">完美适配各种屏幕尺寸</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">键盘支持</div>
                        <div class="feature-desc">方向键、Enter、Esc等快捷操作</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">进度指示</div>
                        <div class="feature-desc">动态进度条和步骤计数</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="feature-text">
                        <div class="feature-title">动画效果</div>
                        <div class="feature-desc">流畅的过渡和交互反馈</div>
                    </div>
                </div>
            </div>
            
            <!-- 演示区域 -->
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>🎬 完整引导流程</h3>
                    <p>体验从欢迎到完成的完整8步引导流程</p>
                    <button class="demo-btn" onclick="startFullGuide()">开始完整引导</button>
                </div>
                
                <div class="demo-card">
                    <h3>🎨 视觉效果对比</h3>
                    <p>查看 guide card.html 原始设计效果</p>
                    <button class="demo-btn" onclick="window.open('guide card.html', '_blank')">打开原始设计</button>
                </div>
                
                <div class="demo-card">
                    <h3>📱 响应式测试</h3>
                    <p>调整浏览器窗口大小测试适配效果</p>
                    <button class="demo-btn" onclick="testResponsive()">测试响应式</button>
                </div>
            </div>
            
            <!-- 模拟页面元素 -->
            <div style="display: flex; gap: 24px; flex-wrap: wrap; margin-top: 32px;">
                <div class="sidebar">
                    <h4 style="margin-bottom: 16px; color: #1e293b;">导航菜单</h4>
                    <div class="menu-item">📊 仪表盘</div>
                    <div class="menu-item">📦 产品库</div>
                    <div class="menu-item">👥 建联记录</div>
                    <div class="menu-item" id="ai-assistant-menu">🤖 AI助手</div>
                    <div class="menu-item">⚙️ 设置</div>
                </div>
                
                <div style="flex: 1; min-width: 300px;">
                    <h4 style="margin-bottom: 16px; color: #1e293b;">AI助手区域</h4>
                    <p style="color: #64748b; margin-bottom: 16px; line-height: 1.6;">
                        使用AI分析您的产品，获得精准的博主推荐和专业的建联邮件模板
                    </p>
                    <button class="new-chat">
                        <i class="fas fa-plus" style="margin-right: 8px;"></i>
                        新建商品分析
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引导相关脚本 -->
    <script src="guide-config.js"></script>
    <script src="onboarding-guide.js"></script>
    <script>
        let currentGuide = null;
        
        // 创建引导实例
        function createGuide() {
            if (currentGuide) {
                currentGuide.stop();
            }
            currentGuide = new OnboardingGuide(ONBOARDING_CONFIG);
            return currentGuide;
        }
        
        // 显示指定步骤
        function showStep(stepIndex) {
            const guide = createGuide();
            guide.currentStep = stepIndex;
            guide.isActive = true;
            
            // 创建引导界面
            guide.createOverlay();
            guide.createTooltip();
            guide.bindEvents();
            
            // 显示步骤
            guide.showStep(stepIndex);
            
            // 更新控制按钮状态
            document.querySelectorAll('.control-btn').forEach((btn, index) => {
                btn.classList.toggle('active', index === stepIndex);
            });
        }
        
        // 停止引导
        function stopGuide() {
            if (currentGuide) {
                currentGuide.stop();
                currentGuide = null;
            }
            
            // 清除按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
        }
        
        // 开始完整引导
        function startFullGuide() {
            // 清除本地存储
            localStorage.removeItem('completed_guides');
            localStorage.removeItem('guide_version');
            localStorage.removeItem('guide_stats');
            
            const guide = createGuide();
            guide.start();
        }
        
        // 测试响应式
        function testResponsive() {
            alert('请调整浏览器窗口大小来测试响应式效果！\n\n• 在移动端尺寸下，引导卡片会适配屏幕\n• 按钮布局会变为垂直排列\n• 图标和字体大小会相应调整');
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.altKey) {
                switch(e.key) {
                    case '1': showStep(0); break;
                    case '2': showStep(1); break;
                    case '3': showStep(2); break;
                    case '4': showStep(3); break;
                    case '5': showStep(4); break;
                    case '6': showStep(5); break;
                    case '7': showStep(6); break;
                    case '8': showStep(7); break;
                    case '0': stopGuide(); break;
                }
            }
        });
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🎯 引导系统测试页面加载完成！');
                console.log('💡 快捷键：Alt + 数字键(1-8) 显示对应步骤，Alt + 0 停止引导');
            }, 1000);
        });
    </script>
</body>
</html> 