# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
bower_components/

# 构建输出
dist/
build/
out/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存
.npm
.eslintcache
.stylelintcache

# 临时文件
*.tmp
*.temp
.cache/

# 备份文件
*.backup
*.bak

# 覆盖率报告
coverage/
*.lcov

# 测试输出
test-results/
playwright-report/

# 包管理器锁定文件（根据项目需要选择）
# package-lock.json
# yarn.lock

# 项目特定
style.css.backup 

# YoYo AI version control directory
.yoyo/
